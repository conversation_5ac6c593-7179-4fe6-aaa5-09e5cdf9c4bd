#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

/// 怪物数据处理字符串常量管理类
pub struct guaiwu_zifuchuan_changliangguanli;

impl guaiwu_zifuchuan_changliangguanli {
    // ==================== 查询模式常量 ====================

    /// 全部信息查询模式
    pub const chaxun_moshi_quanbu_xinxi: &'static str = "quanbu_xinxi";

    // ==================== 数据来源常量 ====================

    /// MySQL数据来源
    pub const shuju_laiyuan_mysql: &'static str = "mysql";

    /// Redis数据来源
    pub const shuju_laiyuan_redis: &'static str = "redis";

    // ==================== 错误信息常量 ====================

    /// 怪物不存在错误信息模板
    pub const cuowu_guaiwu_bucunzai: &'static str = "怪物ID {} 不存在";

    /// 指定字段不在汇总表中错误信息
    pub const cuowu_ziduan_bu_zai_huizong_biao: &'static str = "指定的字段都不在汇总表中";

    /// 怪物在汇总表中不存在错误信息模板
    pub const cuowu_guaiwu_zai_huizong_biao_bucunzai: &'static str = "怪物ID {} 在汇总表中不存在";

    /// 怪物基础信息不存在错误信息
    pub const cuowu_guaiwu_jiben_xinxi_bucunzai: &'static str = "怪物基础信息不存在";

    /// 怪物汇总信息不存在错误信息
    pub const cuowu_guaiwu_huizong_xinxi_bucunzai: &'static str = "怪物汇总信息不存在";

    /// 无效字段错误信息模板
    pub const cuowu_wuxiao_ziduan: &'static str = "以下字段无效: {}";

    /// 没有有效字段错误信息
    pub const cuowu_meiyou_youxiao_ziduan: &'static str = "没有找到任何有效字段";

    /// 查询怪物列表失败错误信息模板
    pub const cuowu_chaxun_liebiao_shibai: &'static str = "查询怪物列表失败: {}";

    /// 查询怪物总数失败错误信息模板
    pub const cuowu_chaxun_zongshu_shibai: &'static str = "查询怪物总数失败: {}";

    /// 清除列表缓存失败错误信息模板
    pub const cuowu_qingchu_liebiao_huancun_shibai: &'static str = "清除列表缓存失败: {}";

    // ==================== 数据库表名常量 ====================

    /// mob_name表名
    pub const biao_ming_mob_name: &'static str = "mob_name";

    /// guaiwu_huizong表名
    pub const biao_ming_guaiwu_huizong: &'static str = "guaiwu_huizong";

    /// 指定字段支持表名（用于说明）
    pub const biao_ming_zhiding_ziduan_zhichi: &'static str = "zhiding_ziduan_zhichi";



    // ==================== Redis相关常量 ====================

    /// Redis键前缀 - 怪物全部信息
    pub const redis_jian_qianzhui_guaiwu_quanbu: &'static str = "guaiwu_quanbu";

    /// Redis键模式 - 怪物全部信息（用于批量删除）
    pub const redis_jian_moshi_guaiwu_quanbu: &'static str = "guaiwu_quanbu:*";

    /// Redis键前缀 - 怪物列表分页
    pub const redis_jian_qianzhui_liebiao_fenye: &'static str = "guaiwu_liebiao_fenye";

    /// Redis键模式 - 怪物列表相关（用于批量删除）
    pub const redis_jian_moshi_liebiao_suoyou: &'static str = "guaiwu_liebiao_*";

    /// 列表缓存时间：5小时（18000秒）
    pub const liebiao_huancun_shijian: u64 = 18000;

    // ==================== 统计信息常量 ====================

    /// 怪物全部信息缓存统计描述
    pub const tongji_guaiwu_quanbu_xinxi_huancun: &'static str = "怪物全部信息缓存: {} 个";

    /// 怪物全部信息缓存获取失败描述
    pub const tongji_guaiwu_quanbu_xinxi_huancun_shibai: &'static str = "怪物全部信息缓存: 获取失败";

    /// 未启用Redis缓存描述
    pub const tongji_wei_qiyong_redis_huancun: &'static str = "未启用Redis缓存";

    /// 怪物列表分页缓存统计描述
    pub const tongji_liebiao_fenye_huancun: &'static str = "怪物列表分页缓存: {} 个";

    /// 列表缓存清除成功描述
    pub const tongji_liebiao_huancun_qingchu_chenggong: &'static str = "怪物列表缓存清除成功";

    /// 列表缓存清除失败描述
    pub const tongji_liebiao_huancun_qingchu_shibai: &'static str = "怪物列表缓存清除失败: {}";

    /// 怪物列表分页缓存获取失败描述
    pub const tongji_liebiao_fenye_huoqu_shibai: &'static str = "怪物列表分页缓存: 获取失败";

    // ==================== 方法：生成动态SQL和错误信息 ====================

    /// 生成怪物不存在错误信息
    pub fn shengcheng_cuowu_guaiwu_bucunzai(guaiwu_id: &str) -> String {
        Self::cuowu_guaiwu_bucunzai.replace("{}", guaiwu_id)
    }

    /// 生成怪物在汇总表中不存在错误信息
    pub fn shengcheng_cuowu_guaiwu_zai_huizong_biao_bucunzai(guaiwu_id: &str) -> String {
        Self::cuowu_guaiwu_zai_huizong_biao_bucunzai.replace("{}", guaiwu_id)
    }



    /// 生成Redis键名 - 怪物全部信息
    pub fn shengcheng_redis_jian_guaiwu_quanbu(guaiwu_id: &str) -> String {
        format!("{}:{}", Self::redis_jian_qianzhui_guaiwu_quanbu, guaiwu_id)
    }

    /// 生成怪物全部信息缓存统计描述
    pub fn shengcheng_tongji_guaiwu_quanbu_xinxi_huancun(shuliang: u64) -> String {
        Self::tongji_guaiwu_quanbu_xinxi_huancun.replace("{}", &shuliang.to_string())
    }

    /// 生成无效字段错误信息
    pub fn shengcheng_cuowu_wuxiao_ziduan(wuxiao_ziduan: &[String]) -> String {
        let ziduan_str = wuxiao_ziduan.join(", ");
        Self::cuowu_wuxiao_ziduan.replace("{}", &ziduan_str)
    }

    /// 生成怪物列表分页Redis缓存键名
    pub fn shengcheng_redis_jian_liebiao_fenye(yema: u32, meiye_shuliang: u32) -> String {
        format!("{}:{}:{}", Self::redis_jian_qianzhui_liebiao_fenye, yema, meiye_shuliang)
    }

    /// 生成怪物列表分页缓存统计信息
    pub fn shengcheng_tongji_liebiao_fenye_huancun(count: u64) -> String {
        Self::tongji_liebiao_fenye_huancun.replace("{}", &count.to_string())
    }

    /// 生成查询怪物列表失败错误信息
    pub fn shengcheng_cuowu_chaxun_liebiao_shibai(cuowu_xinxi: &str) -> String {
        Self::cuowu_chaxun_liebiao_shibai.replace("{}", cuowu_xinxi)
    }

    /// 生成查询怪物总数失败错误信息
    pub fn shengcheng_cuowu_chaxun_zongshu_shibai(cuowu_xinxi: &str) -> String {
        Self::cuowu_chaxun_zongshu_shibai.replace("{}", cuowu_xinxi)
    }

    /// 生成清除列表缓存失败错误信息
    pub fn shengcheng_cuowu_qingchu_liebiao_huancun_shibai(cuowu_xinxi: &str) -> String {
        Self::cuowu_qingchu_liebiao_huancun_shibai.replace("{}", cuowu_xinxi)
    }
}
