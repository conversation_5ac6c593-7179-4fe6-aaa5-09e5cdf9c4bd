#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::guaiwu_rizhi_kongzhi::guaiwu_zifuchuan_changliangguanli;
use super::guaiwujiegouti::guaiwu_wanzheng_xinxi;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use anyhow::Result;

/// 怪物Redis缓存控制类
pub struct guaiwu_redis_kongzhi {
    redis_lianjie: redis_lianjie_guanli,
}

/// 怪物缓存时间常量
impl guaiwu_redis_kongzhi {
    /// 怪物全部信息缓存时间：3天（259200秒）
    pub const quanbu_xinxi_huancun_shijian: u64 = 259200;
}

impl guaiwu_redis_kongzhi {
    /// 创建新的怪物Redis控制实例
    pub fn new(redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            redis_lianjie,
        }
    }

    /// 生成怪物全部信息的Redis键名
    fn shengcheng_quanbu_xinxi_jian(&self, guaiwu_id: &str) -> String {
        guaiwu_zifuchuan_changliangguanli::shengcheng_redis_jian_guaiwu_quanbu(guaiwu_id)
    }

    /// 从Redis获取怪物全部信息
    pub async fn huoqu_quanbu_xinxi(&self, guaiwu_id: &str) -> Result<Option<guaiwu_wanzheng_xinxi>> {
        let jian = self.shengcheng_quanbu_xinxi_jian(guaiwu_id);

        match self.redis_lianjie.huoqu(&jian).await? {
            Some(json_str) => {
                match serde_json::from_str::<guaiwu_wanzheng_xinxi>(&json_str) {
                    Ok(guaiwu_xinxi) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &format!("从Redis成功获取怪物{}的全部信息", guaiwu_id),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Ok(Some(guaiwu_xinxi))
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &format!("从Redis获取怪物{}的全部信息时JSON解析失败: {}", guaiwu_id, e),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Ok(None)
                    }
                }
            }
            None => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("Redis中不存在怪物{}的全部信息缓存", guaiwu_id),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(None)
            }
        }
    }

    /// 向Redis存储怪物全部信息
    pub async fn cunchu_quanbu_xinxi(&self, guaiwu_id: &str, guaiwu_xinxi: &guaiwu_wanzheng_xinxi) -> Result<()> {
        let jian = self.shengcheng_quanbu_xinxi_jian(guaiwu_id);
        let json_str = serde_json::to_string(guaiwu_xinxi)?;

        self.redis_lianjie.shezhi_with_guoqi(&jian, &json_str, Self::quanbu_xinxi_huancun_shijian as i64).await?;

        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            &format!("成功将怪物{}的全部信息存储到Redis", guaiwu_id),
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        Ok(())
    }

    /// 删除怪物全部信息缓存
    pub async fn shanchu_quanbu_xinxi(&self, guaiwu_id: &str) -> Result<bool> {
        let jian = self.shengcheng_quanbu_xinxi_jian(guaiwu_id);
        
        let chenggong = self.redis_lianjie.shanchu(&jian).await?;

        if chenggong {
            crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                &format!("成功删除怪物{}的全部信息缓存", guaiwu_id),
                crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
            );
        } else {
            crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                &format!("怪物{}的全部信息缓存不存在，无需删除", guaiwu_id),
                crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
            );
        }

        Ok(chenggong)
    }

    /// 清理怪物全部数据获取的Redis缓存
    ///
    /// 注意：此方法只清理怪物全部信息缓存（guaiwu_quanbu:*），
    /// 不会影响其他类型的缓存（如分类缓存、列表缓存等）
    pub async fn qingchu_guaiwu_quanbu_xinxi_huancun(&self) -> Result<u64> {
        let moshi = guaiwu_zifuchuan_changliangguanli::redis_jian_moshi_guaiwu_quanbu;

        let shanchu_shuliang = self.redis_lianjie.shanchu_by_pattern(moshi).await?;

        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            &format!("清理怪物全部信息缓存完成，删除了{}个键", shanchu_shuliang),
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
        );

        Ok(shanchu_shuliang)
    }

    /// 获取怪物缓存统计信息
    pub async fn huoqu_guaiwu_huancun_tongji(&self) -> Result<String> {
        let moshi = guaiwu_zifuchuan_changliangguanli::redis_jian_moshi_guaiwu_quanbu;
        
        match self.redis_lianjie.count_keys_by_pattern(moshi).await {
            Ok(shuliang) => {
                Ok(guaiwu_zifuchuan_changliangguanli::shengcheng_tongji_guaiwu_quanbu_xinxi_huancun(shuliang))
            }
            Err(_) => {
                Ok(guaiwu_zifuchuan_changliangguanli::tongji_guaiwu_quanbu_xinxi_huancun_shibai.to_string())
            }
        }
    }

    /// 检查怪物缓存是否存在
    pub async fn jiancha_huancun_cunzai(&self, guaiwu_id: &str) -> Result<bool> {
        let jian = self.shengcheng_quanbu_xinxi_jian(guaiwu_id);
        self.redis_lianjie.cunzai(&jian).await
    }

    /// 刷新怪物缓存过期时间
    pub async fn shuaxin_huancun_guoqi_shijian(&self, guaiwu_id: &str) -> Result<bool> {
        let jian = self.shengcheng_quanbu_xinxi_jian(guaiwu_id);
        self.redis_lianjie.shezhi_guoqi(&jian, Self::quanbu_xinxi_huancun_shijian as i64).await
    }

    /// 获取怪物列表缓存统计信息
    pub async fn huoqu_liebiao_huancun_tongji(&self) -> Result<String> {
        let moshi = guaiwu_zifuchuan_changliangguanli::redis_jian_moshi_liebiao_suoyou;

        match self.redis_lianjie.count_keys_by_pattern(moshi).await {
            Ok(shuliang) => {
                Ok(guaiwu_zifuchuan_changliangguanli::shengcheng_tongji_liebiao_fenye_huancun(shuliang))
            }
            Err(_) => {
                Ok(guaiwu_zifuchuan_changliangguanli::tongji_liebiao_fenye_huoqu_shibai.to_string())
            }
        }
    }

    /// 清除怪物列表缓存
    pub async fn qingchu_liebiao_huancun(&self) -> Result<()> {
        let moshi = guaiwu_zifuchuan_changliangguanli::redis_jian_moshi_liebiao_suoyou;

        match self.redis_lianjie.shanchu_by_pattern(moshi).await {
            Ok(shanchu_shuliang) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("{}，删除了{}个键", guaiwu_zifuchuan_changliangguanli::tongji_liebiao_huancun_qingchu_chenggong, shanchu_shuliang),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(())
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_qingchu_liebiao_huancun_shibai(&e.to_string());
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }
}
