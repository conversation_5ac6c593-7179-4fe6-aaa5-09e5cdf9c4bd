#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

/// 怪物数据处理SQL语句管理类
pub struct guaiwu_sql_guanli;

impl guaiwu_sql_guanli {
    // ==================== 基础查询SQL ====================

    /// 查询mob_name表基础信息的SQL
    pub const sql_chaxun_jiben_xinxi: &'static str =
        "SELECT ID, Aegis_name, Type, schinese, tchinese, en, jp, kr FROM mob_name WHERE ID = ?";

    /// 查询guaiwu_huizong表汇总信息的SQL
    pub const sql_chaxun_huizong_xinxi: &'static str =
        "SELECT * FROM guaiwu_huizong WHERE id = ?";

    // ==================== 存在性检查SQL ====================

    /// 检查mob_name表中怪物是否存在的SQL
    pub const sql_jiancha_jiben_biao_cunzai: &'static str =
        "SELECT COUNT(*) as count FROM mob_name WHERE ID = ?";

    /// 检查guaiwu_huizong表中怪物是否存在的SQL
    pub const sql_jiancha_huizong_biao_cunzai: &'static str =
        "SELECT COUNT(*) as count FROM guaiwu_huizong WHERE id = ?";

    // ==================== 动态SQL生成方法 ====================

    /// 生成查询mob_name表指定字段的SQL
    pub fn shengcheng_sql_chaxun_jiben_biao_ziduan(ziduan_liebiao: &[&str]) -> String {
        if ziduan_liebiao.is_empty() {
            return "SELECT ID FROM mob_name WHERE ID = ?".to_string();
        }
        
        let ziduan_str = ziduan_liebiao.join(", ");
        format!("SELECT {} FROM mob_name WHERE ID = ?", ziduan_str)
    }

    /// 生成查询guaiwu_huizong表指定字段的SQL
    pub fn shengcheng_sql_chaxun_huizong_biao_ziduan(ziduan_liebiao: &[&str]) -> String {
        if ziduan_liebiao.is_empty() {
            return "SELECT id FROM guaiwu_huizong WHERE id = ?".to_string();
        }
        
        let ziduan_str = ziduan_liebiao.join(", ");
        format!("SELECT {} FROM guaiwu_huizong WHERE id = ?", ziduan_str)
    }

    // ==================== 复杂查询SQL ====================

    /// 获取怪物总数的SQL
    pub const sql_huoqu_guaiwu_zongshu: &'static str =
        "SELECT COUNT(*) as total FROM guaiwu_huizong";

    /// 获取有基础信息的怪物数量SQL
    pub const sql_huoqu_you_jichuxinxi_guaiwu_shuliang: &'static str =
        "SELECT COUNT(*) as count FROM guaiwu_huizong WHERE you_jichuxinxi = 'true'";

    /// 获取有掉落物信息的怪物数量SQL
    pub const sql_huoqu_you_diaoluowu_guaiwu_shuliang: &'static str =
        "SELECT COUNT(*) as count FROM guaiwu_huizong WHERE you_diaoluowu = 'true'";

    /// 获取有技能信息的怪物数量SQL
    pub const sql_huoqu_you_jineng_guaiwu_shuliang: &'static str =
        "SELECT COUNT(*) as count FROM guaiwu_huizong WHERE you_jineng = 'true'";

    /// 获取有地图信息的怪物数量SQL
    pub const sql_huoqu_you_ditu_guaiwu_shuliang: &'static str =
        "SELECT COUNT(*) as count FROM guaiwu_huizong WHERE you_ditu = 'true'";

    /// 获取MVP怪物数量SQL
    pub const sql_huoqu_mvp_guaiwu_shuliang: &'static str =
        "SELECT COUNT(*) as count FROM guaiwu_huizong WHERE mvp_flag = '1'";

    /// 获取精英怪物数量SQL
    pub const sql_huoqu_jingyingguai_shuliang: &'static str =
        "SELECT COUNT(*) as count FROM guaiwu_huizong WHERE jingyingguai = 'true'";

    /// 获取宠物怪物数量SQL
    pub const sql_huoqu_chongwu_guaiwu_shuliang: &'static str =
        "SELECT COUNT(*) as count FROM guaiwu_huizong WHERE shi_chongwu = 'true'";

    // ==================== 分页查询SQL ====================

    /// 生成分页查询怪物列表的SQL
    pub fn shengcheng_sql_fenye_chaxun_guaiwu_liebiao(limit: u32, offset: u32) -> String {
        format!(
            "SELECT id, dbname, zhongwenming, level, health 
             FROM guaiwu_huizong 
             ORDER BY id 
             LIMIT {} OFFSET {}",
            limit, offset
        )
    }

    /// 生成按名称搜索怪物的SQL
    pub fn shengcheng_sql_sousuo_guaiwu(sousuo_guanjianci: &str, limit: u32, offset: u32) -> String {
        format!(
            "SELECT id, dbname, zhongwenming, level, health 
             FROM guaiwu_huizong 
             WHERE dbname LIKE '%{}%' 
                OR zhongwenming LIKE '%{}%'
             ORDER BY id 
             LIMIT {} OFFSET {}",
            sousuo_guanjianci, sousuo_guanjianci, limit, offset
        )
    }

    /// 生成按等级范围筛选怪物的SQL
    pub fn shengcheng_sql_andengji_shaixuan_guaiwu(min_level: i32, max_level: i32, limit: u32, offset: u32) -> String {
        format!(
            "SELECT id, dbname, zhongwenming, level, health
             FROM guaiwu_huizong
             WHERE CAST(level AS SIGNED) BETWEEN {} AND {}
             ORDER BY CAST(level AS SIGNED)
             LIMIT {} OFFSET {}",
            min_level, max_level, limit, offset
        )
    }

    /// 生成按种族筛选怪物的SQL
    pub fn shengcheng_sql_anzhongzu_shaixuan_guaiwu(zhongzu_leixing: &str, limit: u32, offset: u32) -> String {
        format!(
            "SELECT id, dbname, zhongwenming, level, health
             FROM guaiwu_huizong
             WHERE {} = 'true'
             ORDER BY id
             LIMIT {} OFFSET {}",
            zhongzu_leixing, limit, offset
        )
    }

    /// 生成按元素筛选怪物的SQL
    pub fn shengcheng_sql_anyuansu_shaixuan_guaiwu(yuansu_leixing: &str, limit: u32, offset: u32) -> String {
        format!(
            "SELECT id, dbname, zhongwenming, level, health
             FROM guaiwu_huizong
             WHERE {} = 'true'
             ORDER BY id
             LIMIT {} OFFSET {}",
            yuansu_leixing, limit, offset
        )
    }

    /// 生成按尺寸筛选怪物的SQL
    pub fn shengcheng_sql_anchicun_shaixuan_guaiwu(chicun_leixing: &str, limit: u32, offset: u32) -> String {
        format!(
            "SELECT id, dbname, zhongwenming, level, health
             FROM guaiwu_huizong
             WHERE {} = 'true'
             ORDER BY id
             LIMIT {} OFFSET {}",
            chicun_leixing, limit, offset
        )
    }

    // ==================== 怪物列表查询SQL ====================

    /// 查询怪物列表SQL（带分页，联表查询优化名称显示）
    pub const sql_chaxun_liebiao_fenye: &'static str = r#"
        SELECT
            h.id as guaiwu_id,
            COALESCE(n.schinese, h.zhongwenming) as guaiwu_mingcheng
        FROM guaiwu_huizong h
        LEFT JOIN mob_name n ON h.id = n.ID
        WHERE h.id IS NOT NULL
        ORDER BY h.id
        LIMIT ? OFFSET ?
    "#;

    /// 查询怪物总数SQL
    pub const sql_chaxun_liebiao_zongshu: &'static str = r#"
        SELECT COUNT(*) as zong_shuliang
        FROM guaiwu_huizong
        WHERE id IS NOT NULL
    "#;

    /// 获取怪物列表的SQL（分页查询，旧版本）
    pub fn shengcheng_sql_guaiwu_liebiao_fenye(limit: u32, offset: u32) -> String {
        format!(
            "SELECT id, dbname, zhongwenming
             FROM guaiwu_huizong
             WHERE id IS NOT NULL
             ORDER BY id
             LIMIT {} OFFSET {}",
            limit, offset
        )
    }

    /// 批量查询怪物名称的SQL
    pub fn shengcheng_sql_piliang_chaxun_guaiwu_mingcheng(id_liebiao: &[&str]) -> String {
        let id_str = id_liebiao.iter()
            .map(|id| format!("'{}'", id))
            .collect::<Vec<String>>()
            .join(",");
        format!(
            "SELECT ID, schinese FROM mob_name WHERE ID IN ({})",
            id_str
        )
    }

    /// 获取MVP怪物列表的SQL
    pub fn shengcheng_sql_mvp_guaiwu_liebiao(limit: u32, offset: u32) -> String {
        format!(
            "SELECT id, dbname, zhongwenming, level, health
             FROM guaiwu_huizong
             WHERE mvp_flag = '1'
             ORDER BY CAST(level AS SIGNED) DESC
             LIMIT {} OFFSET {}",
            limit, offset
        )
    }

    /// 获取精英怪物列表的SQL
    pub fn shengcheng_sql_jingyingguai_liebiao(limit: u32, offset: u32) -> String {
        format!(
            "SELECT id, dbname, zhongwenming, level, health
             FROM guaiwu_huizong
             WHERE jingyingguai = 'true'
             ORDER BY CAST(level AS SIGNED) DESC
             LIMIT {} OFFSET {}",
            limit, offset
        )
    }

    /// 获取宠物怪物列表的SQL
    pub fn shengcheng_sql_chongwu_guaiwu_liebiao(limit: u32, offset: u32) -> String {
        format!(
            "SELECT id, dbname, zhongwenming, level, health
             FROM guaiwu_huizong
             WHERE shi_chongwu = 'true'
             ORDER BY id
             LIMIT {} OFFSET {}",
            limit, offset
        )
    }
}
